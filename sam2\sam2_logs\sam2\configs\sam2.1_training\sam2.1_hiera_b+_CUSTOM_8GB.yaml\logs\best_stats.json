{"Trainer/where": 0.0, "Trainer/epoch": 0, "Trainer/steps_train": 4}
{"Trainer/where": 0.0, "Trainer/epoch": 1, "Trainer/steps_train": 8}
{"Trainer/where": 0.0, "Trainer/epoch": 2, "Trainer/steps_train": 12}
{"Trainer/where": 0.0, "Trainer/epoch": 3, "Trainer/steps_train": 16}
{"Trainer/where": 0.0, "Trainer/epoch": 4, "Trainer/steps_train": 20}
{"Trainer/where": 0.0, "Trainer/epoch": 5, "Trainer/steps_train": 24}
{"Trainer/where": 0.0, "Trainer/epoch": 6, "Trainer/steps_train": 28}
{"Trainer/where": 0.0, "Trainer/epoch": 7, "Trainer/steps_train": 32}
{"Trainer/where": 0.0, "Trainer/epoch": 8, "Trainer/steps_train": 36}
{"Trainer/where": 0.0, "Trainer/epoch": 9, "Trainer/steps_train": 40}
{"Trainer/where": 0.0, "Trainer/epoch": 10, "Trainer/steps_train": 44}
{"Trainer/where": 0.0, "Trainer/epoch": 11, "Trainer/steps_train": 48}
{"Trainer/where": 0.0, "Trainer/epoch": 12, "Trainer/steps_train": 52}
{"Trainer/where": 0.0, "Trainer/epoch": 13, "Trainer/steps_train": 56}
{"Trainer/where": 0.0, "Trainer/epoch": 14, "Trainer/steps_train": 60}
{"Trainer/where": 0.0, "Trainer/epoch": 15, "Trainer/steps_train": 64}
{"Trainer/where": 0.0, "Trainer/epoch": 16, "Trainer/steps_train": 68}
{"Trainer/where": 0.0, "Trainer/epoch": 17, "Trainer/steps_train": 72}
{"Trainer/where": 0.0, "Trainer/epoch": 18, "Trainer/steps_train": 76}
{"Trainer/where": 0.0, "Trainer/epoch": 19, "Trainer/steps_train": 80}
{"Trainer/where": 0.0, "Trainer/epoch": 20, "Trainer/steps_train": 84}
{"Trainer/where": 0.0, "Trainer/epoch": 21, "Trainer/steps_train": 88}
{"Trainer/where": 0.0, "Trainer/epoch": 22, "Trainer/steps_train": 92}
{"Trainer/where": 0.0, "Trainer/epoch": 23, "Trainer/steps_train": 96}
{"Trainer/where": 0.0, "Trainer/epoch": 24, "Trainer/steps_train": 100}
{"Trainer/where": 0.0, "Trainer/epoch": 25, "Trainer/steps_train": 104}
{"Trainer/where": 0.0, "Trainer/epoch": 26, "Trainer/steps_train": 108}
{"Trainer/where": 0.0, "Trainer/epoch": 27, "Trainer/steps_train": 112}
{"Trainer/where": 0.0, "Trainer/epoch": 28, "Trainer/steps_train": 116}
{"Trainer/where": 0.0, "Trainer/epoch": 29, "Trainer/steps_train": 120}
{"Trainer/where": 0.0, "Trainer/epoch": 0, "Trainer/steps_train": 4}
{"Trainer/where": 0.0, "Trainer/epoch": 1, "Trainer/steps_train": 8}
{"Trainer/where": 0.0, "Trainer/epoch": 2, "Trainer/steps_train": 12}
{"Trainer/where": 0.0, "Trainer/epoch": 3, "Trainer/steps_train": 16}
{"Trainer/where": 0.0, "Trainer/epoch": 4, "Trainer/steps_train": 20}
{"Trainer/where": 0.0, "Trainer/epoch": 5, "Trainer/steps_train": 24}
{"Trainer/where": 0.0, "Trainer/epoch": 6, "Trainer/steps_train": 28}
{"Trainer/where": 0.0, "Trainer/epoch": 7, "Trainer/steps_train": 32}
{"Trainer/where": 0.0, "Trainer/epoch": 8, "Trainer/steps_train": 36}
{"Trainer/where": 0.0, "Trainer/epoch": 9, "Trainer/steps_train": 40}
{"Trainer/where": 0.0, "Trainer/epoch": 10, "Trainer/steps_train": 44}
{"Trainer/where": 0.0, "Trainer/epoch": 11, "Trainer/steps_train": 48}
{"Trainer/where": 0.0, "Trainer/epoch": 12, "Trainer/steps_train": 52}
{"Trainer/where": 0.0, "Trainer/epoch": 13, "Trainer/steps_train": 56}
{"Trainer/where": 0.0, "Trainer/epoch": 14, "Trainer/steps_train": 60}
{"Trainer/where": 0.0, "Trainer/epoch": 15, "Trainer/steps_train": 64}
{"Trainer/where": 0.0, "Trainer/epoch": 16, "Trainer/steps_train": 68}
{"Trainer/where": 0.0, "Trainer/epoch": 17, "Trainer/steps_train": 72}
{"Trainer/where": 0.0, "Trainer/epoch": 18, "Trainer/steps_train": 76}
{"Trainer/where": 0.0, "Trainer/epoch": 19, "Trainer/steps_train": 80}
{"Trainer/where": 0.0, "Trainer/epoch": 20, "Trainer/steps_train": 84}
{"Trainer/where": 0.0, "Trainer/epoch": 21, "Trainer/steps_train": 88}
{"Trainer/where": 0.0, "Trainer/epoch": 22, "Trainer/steps_train": 92}
{"Trainer/where": 0.0, "Trainer/epoch": 23, "Trainer/steps_train": 96}
{"Trainer/where": 0.0, "Trainer/epoch": 24, "Trainer/steps_train": 100}
{"Trainer/where": 0.0, "Trainer/epoch": 25, "Trainer/steps_train": 104}
{"Trainer/where": 0.0, "Trainer/epoch": 26, "Trainer/steps_train": 108}
{"Trainer/where": 0.0, "Trainer/epoch": 27, "Trainer/steps_train": 112}
{"Trainer/where": 0.0, "Trainer/epoch": 28, "Trainer/steps_train": 116}
{"Trainer/where": 0.0, "Trainer/epoch": 29, "Trainer/steps_train": 120}
{"Trainer/where": 0.0, "Trainer/epoch": 0, "Trainer/steps_train": 4}
{"Trainer/where": 0.0, "Trainer/epoch": 1, "Trainer/steps_train": 8}
{"Trainer/where": 0.0, "Trainer/epoch": 2, "Trainer/steps_train": 12}
{"Trainer/where": 0.0, "Trainer/epoch": 3, "Trainer/steps_train": 16}
{"Trainer/where": 0.0, "Trainer/epoch": 4, "Trainer/steps_train": 20}
{"Trainer/where": 0.0, "Trainer/epoch": 5, "Trainer/steps_train": 24}
{"Trainer/where": 0.0, "Trainer/epoch": 6, "Trainer/steps_train": 28}
{"Trainer/where": 0.0, "Trainer/epoch": 7, "Trainer/steps_train": 32}
{"Trainer/where": 0.0, "Trainer/epoch": 8, "Trainer/steps_train": 36}
{"Trainer/where": 0.0, "Trainer/epoch": 9, "Trainer/steps_train": 40}
{"Trainer/where": 0.0, "Trainer/epoch": 10, "Trainer/steps_train": 44}
{"Trainer/where": 0.0, "Trainer/epoch": 11, "Trainer/steps_train": 48}
{"Trainer/where": 0.0, "Trainer/epoch": 12, "Trainer/steps_train": 52}
{"Trainer/where": 0.0, "Trainer/epoch": 13, "Trainer/steps_train": 56}
{"Trainer/where": 0.0, "Trainer/epoch": 14, "Trainer/steps_train": 60}
{"Trainer/where": 0.0, "Trainer/epoch": 15, "Trainer/steps_train": 64}
{"Trainer/where": 0.0, "Trainer/epoch": 16, "Trainer/steps_train": 68}
{"Trainer/where": 0.0, "Trainer/epoch": 17, "Trainer/steps_train": 72}
{"Trainer/where": 0.0, "Trainer/epoch": 18, "Trainer/steps_train": 76}
{"Trainer/where": 0.0, "Trainer/epoch": 19, "Trainer/steps_train": 80}
{"Trainer/where": 0.0, "Trainer/epoch": 20, "Trainer/steps_train": 84}
{"Trainer/where": 0.0, "Trainer/epoch": 21, "Trainer/steps_train": 88}
{"Trainer/where": 0.0, "Trainer/epoch": 22, "Trainer/steps_train": 92}
{"Trainer/where": 0.0, "Trainer/epoch": 23, "Trainer/steps_train": 96}
{"Trainer/where": 0.0, "Trainer/epoch": 24, "Trainer/steps_train": 100}
{"Trainer/where": 0.0, "Trainer/epoch": 25, "Trainer/steps_train": 104}
{"Trainer/where": 0.0, "Trainer/epoch": 26, "Trainer/steps_train": 108}
{"Trainer/where": 0.0, "Trainer/epoch": 27, "Trainer/steps_train": 112}
{"Trainer/where": 0.0, "Trainer/epoch": 28, "Trainer/steps_train": 116}
{"Trainer/where": 0.0, "Trainer/epoch": 29, "Trainer/steps_train": 120}
{"Trainer/where": 0.0, "Trainer/epoch": 0, "Trainer/steps_train": 4}
{"Trainer/where": 0.0, "Trainer/epoch": 1, "Trainer/steps_train": 8}
{"Trainer/where": 0.0, "Trainer/epoch": 2, "Trainer/steps_train": 12}
{"Trainer/where": 0.0, "Trainer/epoch": 3, "Trainer/steps_train": 16}
{"Trainer/where": 0.0, "Trainer/epoch": 4, "Trainer/steps_train": 20}
{"Trainer/where": 0.0, "Trainer/epoch": 5, "Trainer/steps_train": 24}
{"Trainer/where": 0.0, "Trainer/epoch": 6, "Trainer/steps_train": 28}
{"Trainer/where": 0.0, "Trainer/epoch": 7, "Trainer/steps_train": 32}
{"Trainer/where": 0.0, "Trainer/epoch": 8, "Trainer/steps_train": 36}
{"Trainer/where": 0.0, "Trainer/epoch": 9, "Trainer/steps_train": 40}
{"Trainer/where": 0.0, "Trainer/epoch": 10, "Trainer/steps_train": 44}
{"Trainer/where": 0.0, "Trainer/epoch": 11, "Trainer/steps_train": 48}
{"Trainer/where": 0.0, "Trainer/epoch": 12, "Trainer/steps_train": 52}
{"Trainer/where": 0.0, "Trainer/epoch": 13, "Trainer/steps_train": 56}
{"Trainer/where": 0.0, "Trainer/epoch": 14, "Trainer/steps_train": 60}
{"Trainer/where": 0.0, "Trainer/epoch": 15, "Trainer/steps_train": 64}
{"Trainer/where": 0.0, "Trainer/epoch": 16, "Trainer/steps_train": 68}
{"Trainer/where": 0.0, "Trainer/epoch": 17, "Trainer/steps_train": 72}
{"Trainer/where": 0.0, "Trainer/epoch": 18, "Trainer/steps_train": 76}
{"Trainer/where": 0.0, "Trainer/epoch": 19, "Trainer/steps_train": 80}
{"Trainer/where": 0.0, "Trainer/epoch": 20, "Trainer/steps_train": 84}
{"Trainer/where": 0.0, "Trainer/epoch": 21, "Trainer/steps_train": 88}
{"Trainer/where": 0.0, "Trainer/epoch": 22, "Trainer/steps_train": 92}
{"Trainer/where": 0.0, "Trainer/epoch": 23, "Trainer/steps_train": 96}
{"Trainer/where": 0.0, "Trainer/epoch": 24, "Trainer/steps_train": 100}
{"Trainer/where": 0.0, "Trainer/epoch": 25, "Trainer/steps_train": 104}
{"Trainer/where": 0.0, "Trainer/epoch": 26, "Trainer/steps_train": 108}
{"Trainer/where": 0.0, "Trainer/epoch": 27, "Trainer/steps_train": 112}
{"Trainer/where": 0.0, "Trainer/epoch": 28, "Trainer/steps_train": 116}
{"Trainer/where": 0.0, "Trainer/epoch": 29, "Trainer/steps_train": 120}
{"Trainer/where": 0.0, "Trainer/epoch": 0, "Trainer/steps_train": 4}
{"Trainer/where": 0.0, "Trainer/epoch": 1, "Trainer/steps_train": 8}
{"Trainer/where": 0.0, "Trainer/epoch": 2, "Trainer/steps_train": 12}
{"Trainer/where": 0.0, "Trainer/epoch": 3, "Trainer/steps_train": 16}
{"Trainer/where": 0.0, "Trainer/epoch": 4, "Trainer/steps_train": 20}
{"Trainer/where": 0.0, "Trainer/epoch": 5, "Trainer/steps_train": 24}
{"Trainer/where": 0.0, "Trainer/epoch": 6, "Trainer/steps_train": 28}
{"Trainer/where": 0.0, "Trainer/epoch": 7, "Trainer/steps_train": 32}
{"Trainer/where": 0.0, "Trainer/epoch": 8, "Trainer/steps_train": 36}
{"Trainer/where": 0.0, "Trainer/epoch": 9, "Trainer/steps_train": 40}
{"Trainer/where": 0.0, "Trainer/epoch": 0, "Trainer/steps_train": 4}
{"Trainer/where": 0.0, "Trainer/epoch": 1, "Trainer/steps_train": 8}
{"Trainer/where": 0.0, "Trainer/epoch": 2, "Trainer/steps_train": 12}
{"Trainer/where": 0.0, "Trainer/epoch": 3, "Trainer/steps_train": 16}
{"Trainer/where": 0.0, "Trainer/epoch": 4, "Trainer/steps_train": 20}
{"Trainer/where": 0.0, "Trainer/epoch": 5, "Trainer/steps_train": 24}
{"Trainer/where": 0.0, "Trainer/epoch": 6, "Trainer/steps_train": 28}
{"Trainer/where": 0.0, "Trainer/epoch": 7, "Trainer/steps_train": 32}
{"Trainer/where": 0.0, "Trainer/epoch": 8, "Trainer/steps_train": 36}
{"Trainer/where": 0.0, "Trainer/epoch": 9, "Trainer/steps_train": 40}
{"Trainer/where": 0.0, "Trainer/epoch": 0, "Trainer/steps_train": 4}
{"Trainer/where": 0.0, "Trainer/epoch": 1, "Trainer/steps_train": 8}
{"Trainer/where": 0.0, "Trainer/epoch": 2, "Trainer/steps_train": 12}
{"Trainer/where": 0.0, "Trainer/epoch": 3, "Trainer/steps_train": 16}
{"Trainer/where": 0.0, "Trainer/epoch": 4, "Trainer/steps_train": 20}
{"Trainer/where": 0.0, "Trainer/epoch": 5, "Trainer/steps_train": 24}
{"Trainer/where": 0.0, "Trainer/epoch": 6, "Trainer/steps_train": 28}
{"Trainer/where": 0.0, "Trainer/epoch": 7, "Trainer/steps_train": 32}
{"Trainer/where": 0.0, "Trainer/epoch": 8, "Trainer/steps_train": 36}
{"Trainer/where": 0.0, "Trainer/epoch": 9, "Trainer/steps_train": 40}
{"Trainer/where": 0.0, "Trainer/epoch": 0, "Trainer/steps_train": 8}
{"Trainer/where": 0.0, "Trainer/epoch": 1, "Trainer/steps_train": 16}
{"Trainer/where": 0.0, "Trainer/epoch": 0, "Trainer/steps_train": 8}
{"Trainer/where": 0.0, "Trainer/epoch": 1, "Trainer/steps_train": 16}
{"Trainer/where": 0.0, "Trainer/epoch": 2, "Trainer/steps_train": 24}
{"Trainer/where": 0.0, "Trainer/epoch": 3, "Trainer/steps_train": 32}
{"Trainer/where": 0.0, "Trainer/epoch": 4, "Trainer/steps_train": 40}
{"Trainer/where": 0.0, "Trainer/epoch": 5, "Trainer/steps_train": 48}
{"Trainer/where": 0.0, "Trainer/epoch": 6, "Trainer/steps_train": 56}
{"Trainer/where": 0.0, "Trainer/epoch": 7, "Trainer/steps_train": 64}
{"Trainer/where": 0.0, "Trainer/epoch": 8, "Trainer/steps_train": 72}
{"Trainer/where": 0.0, "Trainer/epoch": 9, "Trainer/steps_train": 80}
{"Trainer/where": 0.021875, "Trainer/epoch": 0, "Trainer/steps_train": 8}
{"Trainer/where": 0.046875, "Trainer/epoch": 1, "Trainer/steps_train": 16}
{"Trainer/where": 0.071875, "Trainer/epoch": 2, "Trainer/steps_train": 24}
{"Trainer/where": 0.096875, "Trainer/epoch": 3, "Trainer/steps_train": 32}
{"Trainer/where": 0.121875, "Trainer/epoch": 4, "Trainer/steps_train": 40}
{"Trainer/where": 0.146875, "Trainer/epoch": 5, "Trainer/steps_train": 48}
{"Trainer/where": 0.171875, "Trainer/epoch": 6, "Trainer/steps_train": 56}
{"Trainer/where": 0.196875, "Trainer/epoch": 7, "Trainer/steps_train": 64}
{"Trainer/where": 0.221875, "Trainer/epoch": 8, "Trainer/steps_train": 72}
{"Trainer/where": 0.246875, "Trainer/epoch": 9, "Trainer/steps_train": 80}
{"Trainer/where": 0.271875, "Trainer/epoch": 10, "Trainer/steps_train": 88}
{"Trainer/where": 0.296875, "Trainer/epoch": 11, "Trainer/steps_train": 96}
{"Trainer/where": 0.321875, "Trainer/epoch": 12, "Trainer/steps_train": 104}
{"Trainer/where": 0.346875, "Trainer/epoch": 13, "Trainer/steps_train": 112}
{"Trainer/where": 0.371875, "Trainer/epoch": 14, "Trainer/steps_train": 120}
{"Trainer/where": 0.396875, "Trainer/epoch": 15, "Trainer/steps_train": 128}
{"Trainer/where": 0.421875, "Trainer/epoch": 16, "Trainer/steps_train": 136}
{"Trainer/where": 0.446875, "Trainer/epoch": 17, "Trainer/steps_train": 144}
{"Trainer/where": 0.471875, "Trainer/epoch": 18, "Trainer/steps_train": 152}
{"Trainer/where": 0.496875, "Trainer/epoch": 19, "Trainer/steps_train": 160}
{"Trainer/where": 0.521875, "Trainer/epoch": 20, "Trainer/steps_train": 168}
{"Trainer/where": 0.546875, "Trainer/epoch": 21, "Trainer/steps_train": 176}
{"Trainer/where": 0.571875, "Trainer/epoch": 22, "Trainer/steps_train": 184}
{"Trainer/where": 0.596875, "Trainer/epoch": 23, "Trainer/steps_train": 192}
{"Trainer/where": 0.621875, "Trainer/epoch": 24, "Trainer/steps_train": 200}
{"Trainer/where": 0.646875, "Trainer/epoch": 25, "Trainer/steps_train": 208}
{"Trainer/where": 0.671875, "Trainer/epoch": 26, "Trainer/steps_train": 216}
{"Trainer/where": 0.696875, "Trainer/epoch": 27, "Trainer/steps_train": 224}
{"Trainer/where": 0.721875, "Trainer/epoch": 28, "Trainer/steps_train": 232}
{"Trainer/where": 0.746875, "Trainer/epoch": 29, "Trainer/steps_train": 240}
{"Trainer/where": 0.771875, "Trainer/epoch": 30, "Trainer/steps_train": 248}
{"Trainer/where": 0.796875, "Trainer/epoch": 31, "Trainer/steps_train": 256}
{"Trainer/where": 0.821875, "Trainer/epoch": 32, "Trainer/steps_train": 264}
{"Trainer/where": 0.846875, "Trainer/epoch": 33, "Trainer/steps_train": 272}
{"Trainer/where": 0.871875, "Trainer/epoch": 34, "Trainer/steps_train": 280}
{"Trainer/where": 0.896875, "Trainer/epoch": 35, "Trainer/steps_train": 288}
{"Trainer/where": 0.921875, "Trainer/epoch": 36, "Trainer/steps_train": 296}
{"Trainer/where": 0.946875, "Trainer/epoch": 37, "Trainer/steps_train": 304}
{"Trainer/where": 0.971875, "Trainer/epoch": 38, "Trainer/steps_train": 312}
{"Trainer/where": 0.996875, "Trainer/epoch": 39, "Trainer/steps_train": 320}
{"Trainer/where": 0.025735294117647058, "Trainer/epoch": 0, "Trainer/steps_train": 8}
{"Trainer/where": 0.05514705882352941, "Trainer/epoch": 1, "Trainer/steps_train": 16}
{"Trainer/where": 0.08455882352941177, "Trainer/epoch": 2, "Trainer/steps_train": 24}
{"Trainer/where": 0.11397058823529412, "Trainer/epoch": 3, "Trainer/steps_train": 32}
{"Trainer/where": 0.14338235294117646, "Trainer/epoch": 4, "Trainer/steps_train": 40}
{"Trainer/where": 0.17279411764705882, "Trainer/epoch": 5, "Trainer/steps_train": 48}
{"Trainer/where": 0.20220588235294118, "Trainer/epoch": 6, "Trainer/steps_train": 56}
{"Trainer/where": 0.23161764705882354, "Trainer/epoch": 7, "Trainer/steps_train": 64}
{"Trainer/where": 0.2610294117647059, "Trainer/epoch": 8, "Trainer/steps_train": 72}
{"Trainer/where": 0.29044117647058826, "Trainer/epoch": 9, "Trainer/steps_train": 80}
{"Trainer/where": 0.31985294117647056, "Trainer/epoch": 10, "Trainer/steps_train": 88}
{"Trainer/where": 0.3492647058823529, "Trainer/epoch": 11, "Trainer/steps_train": 96}
{"Trainer/where": 0.3786764705882353, "Trainer/epoch": 12, "Trainer/steps_train": 104}
{"Trainer/where": 0.40808823529411764, "Trainer/epoch": 13, "Trainer/steps_train": 112}
{"Trainer/where": 0.4375, "Trainer/epoch": 14, "Trainer/steps_train": 120}
{"Trainer/where": 0.46691176470588236, "Trainer/epoch": 15, "Trainer/steps_train": 128}
{"Trainer/where": 0.4963235294117647, "Trainer/epoch": 16, "Trainer/steps_train": 136}
{"Trainer/where": 0.5257352941176471, "Trainer/epoch": 17, "Trainer/steps_train": 144}
{"Trainer/where": 0.5551470588235294, "Trainer/epoch": 18, "Trainer/steps_train": 152}
{"Trainer/where": 0.5845588235294118, "Trainer/epoch": 19, "Trainer/steps_train": 160}
{"Trainer/where": 0.6139705882352942, "Trainer/epoch": 20, "Trainer/steps_train": 168}
{"Trainer/where": 0.6433823529411765, "Trainer/epoch": 21, "Trainer/steps_train": 176}
{"Trainer/where": 0.6727941176470589, "Trainer/epoch": 22, "Trainer/steps_train": 184}
{"Trainer/where": 0.7022058823529411, "Trainer/epoch": 23, "Trainer/steps_train": 192}
{"Trainer/where": 0.7316176470588235, "Trainer/epoch": 24, "Trainer/steps_train": 200}
{"Trainer/where": 0.7610294117647058, "Trainer/epoch": 25, "Trainer/steps_train": 208}
{"Trainer/where": 0.7904411764705882, "Trainer/epoch": 26, "Trainer/steps_train": 216}
{"Trainer/where": 0.8198529411764706, "Trainer/epoch": 27, "Trainer/steps_train": 224}
{"Trainer/where": 0.8492647058823529, "Trainer/epoch": 28, "Trainer/steps_train": 232}
{"Trainer/where": 0.8786764705882353, "Trainer/epoch": 29, "Trainer/steps_train": 240}
{"Trainer/where": 0.9080882352941176, "Trainer/epoch": 30, "Trainer/steps_train": 248}
{"Trainer/where": 0.9375, "Trainer/epoch": 31, "Trainer/steps_train": 256}
{"Trainer/where": 0.9669117647058824, "Trainer/epoch": 32, "Trainer/steps_train": 264}
{"Trainer/where": 0.9963235294117647, "Trainer/epoch": 33, "Trainer/steps_train": 272}
{"Trainer/where": 0.025735294117647058, "Trainer/epoch": 0, "Trainer/steps_train": 8}
{"Trainer/where": 0.05514705882352941, "Trainer/epoch": 1, "Trainer/steps_train": 16}
{"Trainer/where": 0.08455882352941177, "Trainer/epoch": 2, "Trainer/steps_train": 24}
{"Trainer/where": 0.11397058823529412, "Trainer/epoch": 3, "Trainer/steps_train": 32}
{"Trainer/where": 0.14338235294117646, "Trainer/epoch": 4, "Trainer/steps_train": 40}
{"Trainer/where": 0.17279411764705882, "Trainer/epoch": 5, "Trainer/steps_train": 48}
{"Trainer/where": 0.20220588235294118, "Trainer/epoch": 6, "Trainer/steps_train": 56}
{"Trainer/where": 0.23161764705882354, "Trainer/epoch": 7, "Trainer/steps_train": 64}
{"Trainer/where": 0.2610294117647059, "Trainer/epoch": 8, "Trainer/steps_train": 72}
{"Trainer/where": 0.29044117647058826, "Trainer/epoch": 9, "Trainer/steps_train": 80}
{"Trainer/where": 0.31985294117647056, "Trainer/epoch": 10, "Trainer/steps_train": 88}
{"Trainer/where": 0.3492647058823529, "Trainer/epoch": 11, "Trainer/steps_train": 96}
{"Trainer/where": 0.3786764705882353, "Trainer/epoch": 12, "Trainer/steps_train": 104}
{"Trainer/where": 0.40808823529411764, "Trainer/epoch": 13, "Trainer/steps_train": 112}
{"Trainer/where": 0.4375, "Trainer/epoch": 14, "Trainer/steps_train": 120}
{"Trainer/where": 0.46691176470588236, "Trainer/epoch": 15, "Trainer/steps_train": 128}
{"Trainer/where": 0.4963235294117647, "Trainer/epoch": 16, "Trainer/steps_train": 136}
{"Trainer/where": 0.5257352941176471, "Trainer/epoch": 17, "Trainer/steps_train": 144}
{"Trainer/where": 0.5551470588235294, "Trainer/epoch": 18, "Trainer/steps_train": 152}
{"Trainer/where": 0.5845588235294118, "Trainer/epoch": 19, "Trainer/steps_train": 160}
{"Trainer/where": 0.6139705882352942, "Trainer/epoch": 20, "Trainer/steps_train": 168}
{"Trainer/where": 0.6433823529411765, "Trainer/epoch": 21, "Trainer/steps_train": 176}
{"Trainer/where": 0.6727941176470589, "Trainer/epoch": 22, "Trainer/steps_train": 184}
{"Trainer/where": 0.7022058823529411, "Trainer/epoch": 23, "Trainer/steps_train": 192}
{"Trainer/where": 0.7316176470588235, "Trainer/epoch": 24, "Trainer/steps_train": 200}
{"Trainer/where": 0.7610294117647058, "Trainer/epoch": 25, "Trainer/steps_train": 208}
{"Trainer/where": 0.7904411764705882, "Trainer/epoch": 26, "Trainer/steps_train": 216}
{"Trainer/where": 0.8198529411764706, "Trainer/epoch": 27, "Trainer/steps_train": 224}
{"Trainer/where": 0.8492647058823529, "Trainer/epoch": 28, "Trainer/steps_train": 232}
{"Trainer/where": 0.8786764705882353, "Trainer/epoch": 29, "Trainer/steps_train": 240}
{"Trainer/where": 0.9080882352941176, "Trainer/epoch": 30, "Trainer/steps_train": 248}
{"Trainer/where": 0.9375, "Trainer/epoch": 31, "Trainer/steps_train": 256}
{"Trainer/where": 0.9669117647058824, "Trainer/epoch": 32, "Trainer/steps_train": 264}
{"Trainer/where": 0.9963235294117647, "Trainer/epoch": 33, "Trainer/steps_train": 272}
{"Trainer/where": 0.175, "Trainer/epoch": 0, "Trainer/steps_train": 8}
{"Trainer/where": 0.375, "Trainer/epoch": 1, "Trainer/steps_train": 16}
{"Trainer/where": 0.575, "Trainer/epoch": 2, "Trainer/steps_train": 24}
{"Trainer/where": 0.775, "Trainer/epoch": 3, "Trainer/steps_train": 32}
{"Trainer/where": 0.975, "Trainer/epoch": 4, "Trainer/steps_train": 40}
{"Trainer/where": 0.175, "Trainer/epoch": 0, "Trainer/steps_train": 8}
{"Trainer/where": 0.375, "Trainer/epoch": 1, "Trainer/steps_train": 16}
{"Trainer/where": 0.575, "Trainer/epoch": 2, "Trainer/steps_train": 24}
{"Trainer/where": 0.775, "Trainer/epoch": 3, "Trainer/steps_train": 32}
{"Trainer/where": 0.975, "Trainer/epoch": 4, "Trainer/steps_train": 40}
{"Trainer/where": 0.175, "Trainer/epoch": 0, "Trainer/steps_train": 8}
{"Trainer/where": 0.375, "Trainer/epoch": 1, "Trainer/steps_train": 16}
{"Trainer/where": 0.575, "Trainer/epoch": 2, "Trainer/steps_train": 24}
{"Trainer/where": 0.775, "Trainer/epoch": 3, "Trainer/steps_train": 32}
{"Trainer/where": 0.975, "Trainer/epoch": 4, "Trainer/steps_train": 40}
{"Trainer/where": 0.235, "Trainer/epoch": 5, "Trainer/steps_train": 48}
{"Trainer/where": 0.275, "Trainer/epoch": 6, "Trainer/steps_train": 56}
{"Trainer/where": 0.315, "Trainer/epoch": 7, "Trainer/steps_train": 64}
{"Trainer/where": 0.355, "Trainer/epoch": 8, "Trainer/steps_train": 72}
{"Trainer/where": 0.395, "Trainer/epoch": 9, "Trainer/steps_train": 80}
{"Trainer/where": 0.435, "Trainer/epoch": 10, "Trainer/steps_train": 88}
{"Trainer/where": 0.475, "Trainer/epoch": 11, "Trainer/steps_train": 96}
{"Trainer/where": 0.515, "Trainer/epoch": 12, "Trainer/steps_train": 104}
{"Trainer/where": 0.555, "Trainer/epoch": 13, "Trainer/steps_train": 112}
{"Trainer/where": 0.595, "Trainer/epoch": 14, "Trainer/steps_train": 120}
{"Trainer/where": 0.635, "Trainer/epoch": 15, "Trainer/steps_train": 128}
{"Trainer/where": 0.675, "Trainer/epoch": 16, "Trainer/steps_train": 136}
{"Trainer/where": 0.715, "Trainer/epoch": 17, "Trainer/steps_train": 144}
{"Trainer/where": 0.755, "Trainer/epoch": 18, "Trainer/steps_train": 152}
{"Trainer/where": 0.795, "Trainer/epoch": 19, "Trainer/steps_train": 160}
{"Trainer/where": 0.835, "Trainer/epoch": 20, "Trainer/steps_train": 168}
{"Trainer/where": 0.875, "Trainer/epoch": 21, "Trainer/steps_train": 176}
{"Trainer/where": 0.915, "Trainer/epoch": 22, "Trainer/steps_train": 184}
{"Trainer/where": 0.955, "Trainer/epoch": 23, "Trainer/steps_train": 192}
{"Trainer/where": 0.99, "Trainer/epoch": 24, "Trainer/steps_train": 196}
{"Trainer/where": 0.03, "Trainer/epoch": 0, "Trainer/steps_train": 4}
{"Trainer/where": 0.07, "Trainer/epoch": 1, "Trainer/steps_train": 8}
{"Trainer/where": 0.11, "Trainer/epoch": 2, "Trainer/steps_train": 12}
{"Trainer/where": 0.15, "Trainer/epoch": 3, "Trainer/steps_train": 16}
{"Trainer/where": 0.19, "Trainer/epoch": 4, "Trainer/steps_train": 20}
{"Trainer/where": 0.23, "Trainer/epoch": 5, "Trainer/steps_train": 24}
{"Trainer/where": 0.27, "Trainer/epoch": 6, "Trainer/steps_train": 28}
{"Trainer/where": 0.31, "Trainer/epoch": 7, "Trainer/steps_train": 32}
{"Trainer/where": 0.35, "Trainer/epoch": 8, "Trainer/steps_train": 36}
{"Trainer/where": 0.39, "Trainer/epoch": 9, "Trainer/steps_train": 40}
{"Trainer/where": 0.43, "Trainer/epoch": 10, "Trainer/steps_train": 44}
{"Trainer/where": 0.47, "Trainer/epoch": 11, "Trainer/steps_train": 48}
{"Trainer/where": 0.51, "Trainer/epoch": 12, "Trainer/steps_train": 52}
{"Trainer/where": 0.55, "Trainer/epoch": 13, "Trainer/steps_train": 56}
{"Trainer/where": 0.59, "Trainer/epoch": 14, "Trainer/steps_train": 60}
{"Trainer/where": 0.63, "Trainer/epoch": 15, "Trainer/steps_train": 64}
{"Trainer/where": 0.67, "Trainer/epoch": 16, "Trainer/steps_train": 68}
{"Trainer/where": 0.71, "Trainer/epoch": 17, "Trainer/steps_train": 72}
{"Trainer/where": 0.75, "Trainer/epoch": 18, "Trainer/steps_train": 76}
{"Trainer/where": 0.79, "Trainer/epoch": 19, "Trainer/steps_train": 80}
{"Trainer/where": 0.83, "Trainer/epoch": 20, "Trainer/steps_train": 84}
{"Trainer/where": 0.87, "Trainer/epoch": 21, "Trainer/steps_train": 88}
{"Trainer/where": 0.91, "Trainer/epoch": 22, "Trainer/steps_train": 92}
{"Trainer/where": 0.95, "Trainer/epoch": 23, "Trainer/steps_train": 96}
{"Trainer/where": 0.99, "Trainer/epoch": 24, "Trainer/steps_train": 100}
