#!/usr/bin/env python3
"""
Test script to verify the path separator fix for SAM2 video inference.
This script tests the load_video_frames_from_jpg_images function with mixed path separators.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add sam2 to path
sys.path.insert(0, 'sam2')
sys.path.insert(0, 'sam2/sam2')

def test_path_normalization():
    """Test that path normalization works correctly with mixed separators."""
    print("Testing path normalization...")

    # Test cases with mixed separators
    test_paths = [
        "./sam2/datasets/CUSTOM/JPEGImages/480p\\bedroom",
        "./sam2/datasets/CUSTOM/JPEGImages/480p/bedroom",
        "sam2\\datasets\\CUSTOM\\JPEGImages\\480p/bedroom",
        "sam2/datasets/CUSTOM/JPEGImages/480p\\bedroom",
    ]

    all_normalized_correctly = True
    for test_path in test_paths:
        print(f"Original path: {test_path}")
        normalized = os.path.normpath(test_path)
        print(f"Normalized:   {normalized}")
        exists = os.path.exists(normalized)
        print(f"Exists:       {exists}")

        # Check if normalization worked (no mixed separators)
        has_mixed_separators = ('/' in normalized and '\\' in normalized)
        if has_mixed_separators:
            all_normalized_correctly = False
        print()

    return all_normalized_correctly

def test_load_video_frames_function():
    """Test the actual load_video_frames_from_jpg_images function."""
    print("Testing load_video_frames_from_jpg_images function...")
    
    try:
        from sam2.utils.misc import load_video_frames_from_jpg_images
        
        # Test with existing dataset path
        test_path = "./sam2/datasets/CUSTOM/JPEGImages/480p/bedroom"
        mixed_path = "./sam2/datasets/CUSTOM/JPEGImages/480p\\bedroom"
        
        print(f"Testing with normal path: {test_path}")
        print(f"Path exists: {os.path.exists(test_path)}")
        
        print(f"Testing with mixed separators: {mixed_path}")
        print(f"Path exists after normpath: {os.path.exists(os.path.normpath(mixed_path))}")
        
        if os.path.exists(test_path):
            print("Attempting to load frames...")
            try:
                # Test with a small image size to avoid memory issues
                frames, height, width = load_video_frames_from_jpg_images(
                    video_path=mixed_path,  # Use mixed separator path
                    image_size=64,
                    offload_video_to_cpu=True,
                    async_loading_frames=False
                )
                print(f"✅ SUCCESS: Loaded {len(frames)} frames, size: {height}x{width}")
                return True
            except Exception as e:
                print(f"❌ ERROR loading frames: {e}")
                return False
        else:
            print("⚠️  Test path does not exist, skipping frame loading test")
            return True
            
    except ImportError as e:
        print(f"❌ ERROR importing function: {e}")
        return False

def test_vos_inference_path_construction():
    """Test VOS inference path construction."""
    print("Testing VOS inference path construction...")
    
    base_video_dir = "./sam2/datasets/CUSTOM/JPEGImages/480p"
    video_name = "bedroom"
    
    # Simulate the path construction in VOS inference
    video_dir = os.path.normpath(os.path.join(base_video_dir, video_name))
    print(f"Base dir: {base_video_dir}")
    print(f"Video name: {video_name}")
    print(f"Constructed path: {video_dir}")
    print(f"Path exists: {os.path.exists(video_dir)}")
    
    if os.path.exists(video_dir):
        try:
            frame_files = [
                f for f in os.listdir(video_dir)
                if os.path.splitext(f)[-1] in [".jpg", ".jpeg", ".JPG", ".JPEG"]
            ]
            print(f"Found {len(frame_files)} JPEG files")
            if frame_files:
                print(f"First few files: {frame_files[:5]}")
            return True
        except Exception as e:
            print(f"❌ ERROR listing files: {e}")
            return False
    else:
        print("⚠️  Path does not exist")
        return True

def main():
    """Run all tests."""
    print("=" * 60)
    print("SAM2 Path Separator Fix Test")
    print("=" * 60)
    
    # Change to the correct directory
    if not os.path.exists('sam2'):
        print("❌ ERROR: sam2 directory not found. Please run from the correct directory.")
        return False
    
    tests = [
        test_path_normalization,
        test_vos_inference_path_construction,
        test_load_video_frames_function,
    ]
    
    results = []
    for test in tests:
        print("-" * 40)
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ ERROR in {test.__name__}: {e}")
            results.append(False)
        print()
    
    print("=" * 60)
    print("Test Results:")
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    all_passed = all(results)
    print(f"\nOverall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
